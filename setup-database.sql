-- Veritas Chat AI Database Setup Script
-- Run this script in your Supabase SQL Editor to create the required tables

-- Enable UUID extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create messages table
CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at on conversations
DROP TRIGGER IF EXISTS update_conversations_updated_at ON conversations;
CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- Create policies for conversations table
DROP POLICY IF EXISTS "Allow all operations on conversations" ON conversations;
CREATE POLICY "Allow all operations on conversations" ON conversations
    FOR ALL USING (true);

-- Create policies for messages table
DROP POLICY IF EXISTS "Allow all operations on messages" ON messages;
CREATE POLICY "Allow all operations on messages" ON messages
    FOR ALL USING (true);

-- Storage bucket policies for file uploads
-- Note: These policies allow public access to the 'documents' bucket
-- Modify as needed for your security requirements

-- Create policies for storage.objects (file uploads)
DROP POLICY IF EXISTS "Allow uploads to documents bucket" ON storage.objects;
CREATE POLICY "Allow uploads to documents bucket" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'documents');

DROP POLICY IF EXISTS "Allow reads from documents bucket" ON storage.objects;
CREATE POLICY "Allow reads from documents bucket" ON storage.objects
    FOR SELECT USING (bucket_id = 'documents');

DROP POLICY IF EXISTS "Allow updates to documents bucket" ON storage.objects;
CREATE POLICY "Allow updates to documents bucket" ON storage.objects
    FOR UPDATE USING (bucket_id = 'documents');

DROP POLICY IF EXISTS "Allow deletes from documents bucket" ON storage.objects;
CREATE POLICY "Allow deletes from documents bucket" ON storage.objects
    FOR DELETE USING (bucket_id = 'documents');

-- Optional: Create more restrictive policies based on user_id
-- Uncomment and modify these if you want user-specific access control

/*
-- Policy for conversations - users can only access their own conversations
DROP POLICY IF EXISTS "Users can access own conversations" ON conversations;
CREATE POLICY "Users can access own conversations" ON conversations
    FOR ALL USING (auth.uid()::text = user_id);

-- Policy for messages - users can only access messages from their conversations
DROP POLICY IF EXISTS "Users can access own messages" ON messages;
CREATE POLICY "Users can access own messages" ON messages
    FOR ALL USING (
        conversation_id IN (
            SELECT id FROM conversations WHERE user_id = auth.uid()::text
        )
    );
*/

-- Insert sample data (optional - remove if not needed)
-- This creates a sample conversation with a few messages

DO $$
DECLARE
    sample_conversation_id UUID;
BEGIN
    -- Insert sample conversation
    INSERT INTO conversations (title, user_id, metadata)
    VALUES ('Sample Conversation', 'sample-user', '{"source": "setup-script"}')
    RETURNING id INTO sample_conversation_id;
    
    -- Insert sample messages
    INSERT INTO messages (conversation_id, role, content, metadata) VALUES
    (sample_conversation_id, 'user', 'Hello! Can you help me understand how this chat system works?', '{}'),
    (sample_conversation_id, 'assistant', 'Hello! I''d be happy to help you understand how this chat system works. This is a modern chat application built with vanilla JavaScript and powered by Google''s Gemini AI. Here are the key features:

**🎯 Core Features:**
- **Real-time conversations** with AI powered by Gemini 2.5 Flash
- **Persistent chat history** stored in Supabase database
- **Multiple conversations** - you can create and switch between different chats
- **Fast typing animations** that simulate real-time responses
- **Automatic title generation** based on conversation context

**💾 Data Storage:**
- All conversations and messages are stored in a Supabase PostgreSQL database
- Each conversation has a unique UUID for identification
- Messages are linked to conversations through foreign keys
- Everything is automatically saved as you chat

**🔧 Technical Stack:**
- Frontend: Vanilla HTML, CSS, JavaScript
- AI: Google AI Studio Gemini 2.5 Flash API  
- Database: Supabase (PostgreSQL)
- Styling: Modern dark theme similar to ChatGPT

Is there anything specific about the system you''d like me to explain in more detail?

/title Chat System Overview', '{}'),
    (sample_conversation_id, 'user', 'That''s really helpful! How does the UUID system work for organizing conversations?', '{}'),
    (sample_conversation_id, 'assistant', 'Great question! The UUID system is the backbone of how conversations are organized and stored. Here''s how it works:

**🔑 UUID Structure:**
- Each conversation gets a unique UUID (like `550e8400-e29b-41d4-a716-************`)
- UUIDs are generated automatically by PostgreSQL using `gen_random_uuid()`
- They''re virtually impossible to duplicate, ensuring each conversation is unique

**📊 Database Relationships:**
```
conversations table:
├── id (UUID) - Primary key
├── title (TEXT) - "Chat System Overview"  
├── created_at (TIMESTAMP)
├── updated_at (TIMESTAMP)
└── user_id (TEXT) - Optional user identification

messages table:
├── id (UUID) - Primary key
├── conversation_id (UUID) - Links to conversations.id
├── role (TEXT) - "user" or "assistant"
├── content (TEXT) - The actual message
└── created_at (TIMESTAMP)
```

**🔄 How It Works:**
1. **New Chat Created** → New UUID generated for conversation
2. **Messages Added** → Each message references the conversation UUID
3. **Loading Chat** → Query all messages WHERE conversation_id = UUID
4. **Switching Chats** → Load different UUID''s messages

**⚡ Benefits:**
- **Scalable**: Can handle millions of conversations
- **Fast Queries**: Indexed by UUID for quick lookups  
- **Data Integrity**: Foreign key constraints prevent orphaned messages
- **Privacy**: UUIDs are unpredictable, adding security

This system ensures your chat history is perfectly organized and can be retrieved instantly!

/title UUID Organization System', '{}');
    
    RAISE NOTICE 'Sample conversation created with ID: %', sample_conversation_id;
END $$;

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE '✅ Database setup completed successfully!';
    RAISE NOTICE '📋 Tables created: conversations, messages';
    RAISE NOTICE '🔍 Indexes created for optimal performance';
    RAISE NOTICE '🔒 Row Level Security enabled for tables and storage';
    RAISE NOTICE '📁 Storage policies created for documents bucket';
    RAISE NOTICE '📝 Sample conversation added for testing';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Next steps:';
    RAISE NOTICE '1. Update supabase-config.js with your Supabase URL and API key';
    RAISE NOTICE '2. Include Supabase JS library in your HTML';
    RAISE NOTICE '3. Test the connection with your chat application';
END $$;
