// Supabase Configuration for Veritas Chat AI
const SUPABASE_CONFIG = {
    // Supabase Project Configuration
    SUPABASE_URL: 'https://ahxeyhqsicgeplasivfm.supabase.co', // Add your Supabase project URL here
    SUPABASE_ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFoeGV5aHFzaWNnZXBsYXNpdmZtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4NzE0OTcsImV4cCI6MjA2NzQ0NzQ5N30.XgA5-GOXVbapFPmXWTX3tuLfIzCof5ZxlPcoKiWGGeA', // Add your Supabase anon key here
    
    // Table Names
    TABLES: {
        CONVERSATIONS: 'conversations',
        MESSAGES: 'messages'
    },
    
    // Database Schema Information
    SCHEMA: {
        CONVERSATIONS: {
            id: 'UUID PRIMARY KEY',
            title: 'TEXT NOT NULL',
            created_at: 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()',
            updated_at: 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()',
            user_id: 'TEXT', // Optional: for user identification
            metadata: 'JSONB' // Optional: for additional data
        },
        MESSAGES: {
            id: 'UUID PRIMARY KEY',
            conversation_id: 'UUID REFERENCES conversations(id) ON DELETE CASCADE',
            role: 'TEXT NOT NULL CHECK (role IN (\'user\', \'assistant\'))',
            content: 'TEXT NOT NULL',
            created_at: 'TIMESTAMP WITH TIME ZONE DEFAULT NOW()',
            metadata: 'JSONB' // Optional: for additional message data
        },

    }
};

// SQL Scripts for Database Setup
const DATABASE_SETUP = {
    // Create conversations table
    CREATE_CONVERSATIONS_TABLE: `
        CREATE TABLE IF NOT EXISTS conversations (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            title TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            user_id TEXT,
            metadata JSONB DEFAULT '{}'::jsonb
        );
    `,
    
    // Create messages table
    CREATE_MESSAGES_TABLE: `
        CREATE TABLE IF NOT EXISTS messages (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
            role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
            content TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            metadata JSONB DEFAULT '{}'::jsonb
        );
    `,


    // Create indexes for better performance
    CREATE_INDEXES: `
        CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
        CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
        CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
        CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
    `,
    
    // Create updated_at trigger for conversations
    CREATE_UPDATED_AT_TRIGGER: `
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        CREATE TRIGGER update_conversations_updated_at 
            BEFORE UPDATE ON conversations 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
    `,
    
    // Row Level Security (RLS) policies
    ENABLE_RLS: `
        ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
        ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

        -- Allow all operations for now (you can restrict based on user_id later)
        CREATE POLICY "Allow all operations on conversations" ON conversations
            FOR ALL USING (true);

        CREATE POLICY "Allow all operations on messages" ON messages
            FOR ALL USING (true);
    `
};

// Export configuration
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SUPABASE_CONFIG, DATABASE_SETUP };
} else {
    window.SUPABASE_CONFIG = SUPABASE_CONFIG;
    window.DATABASE_SETUP = DATABASE_SETUP;
}
