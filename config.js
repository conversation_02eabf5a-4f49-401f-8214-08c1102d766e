// Configuration for Veritas Chat AI
const CONFIG = {
    // Google AI Studio API Configuration
    GEMINI_API_KEY: 'AIzaSyC0BmHRq0-po---FzQWFq9NQv8eczI9ouk', // Add your API key here from https://aistudio.google.com/app/apikey
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent',

    // Model Configuration
    MODEL_NAME: 'gemini-2.5-flash-lite-preview-06-17',
    MAX_TOKENS: 65536,
    TEMPERATURE: 0.5,

    // Grounding Tools Configuration
    GROUNDING_TOOLS: {
        GOOGLE_SEARCH: {
            google_search: {}
        }
    },

    // App Configuration
    APP_NAME: 'Veritas Chat AI',
    APP_VERSION: '1.0.0',
    POWERED_BY: 'Google AI Studio - Gemini 2.5 Flash Lite Preview',

    // Safety Settings
    SAFETY_SETTINGS: [
        {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_NONE'
        },
        {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_NONE'
        },
        {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_NONE'
        },
        {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_NONE'
        }
    ],
    
    // Generation Configuration
    GENERATION_CONFIG: {
        temperature: 0.5,
        topK: 80,
        topP: 0.95,
        maxOutputTokens: 65536,
        responseMimeType: 'text/plain'
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}
