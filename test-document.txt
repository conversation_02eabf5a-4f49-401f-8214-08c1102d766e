Test Document for Upload

This is a test document to verify that the document upload functionality is working correctly.

Key Features to Test:
1. Text extraction from plain text files
2. Character and word counting
3. Document preview display
4. Content cleaning and normalization

Sample Content:
- This document contains multiple paragraphs
- It has bullet points and formatting
- There are numbers: 123, 456, 789
- Special characters: @#$%^&*()
- Unicode characters: ñáéíóú

The document upload system should:
✓ Extract all this text content
✓ Count words and characters accurately
✓ Display a nice preview
✓ Clean up any formatting issues
✓ Send the content to the AI for analysis

End of test document.
