# Supabase Setup Guide for Veritas Chat AI

This guide will help you set up Supabase database integration for persistent conversation history with UUID-based organization.

## 🚀 Quick Setup Steps

### 1. Create Supabase Project

1. Go to [Supabase](https://supabase.com)
2. Sign up/Sign in with your account
3. Click "New Project"
4. Choose your organization
5. Fill in project details:
   - **Name**: `veritas-chat-ai`
   - **Database Password**: Create a strong password
   - **Region**: Choose closest to your users
6. Click "Create new project"
7. Wait for project to be ready (2-3 minutes)

### 2. Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy these values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 3. Configure Your Application

1. Open `supabase-config.js` in your project
2. Update the configuration:

```javascript
const SUPABASE_CONFIG = {
    SUPABASE_URL: 'https://your-project-id.supabase.co',
    SUPABASE_ANON_KEY: 'your-anon-key-here',
    // ... rest of config stays the same
};
```

### 4. Create Database Tables

1. In Supabase dashboard, go to **SQL Editor**
2. Click "New query"
3. Copy the entire contents of `setup-database.sql`
4. Paste it into the SQL editor
5. Click "Run" to execute the script

The script will create:
- ✅ `conversations` table with UUID primary keys
- ✅ `messages` table linked to conversations
- ✅ Indexes for optimal performance
- ✅ Triggers for automatic timestamps
- ✅ Row Level Security policies
- ✅ Sample data for testing

### 5. Test Your Setup

1. Open your chat application in a browser
2. Check the browser console for messages:
   - ✅ "Supabase client initialized successfully"
   - ✅ "Loading conversations from database..."
   - ✅ "Loaded X conversations from database"

## 📊 Database Schema

### Conversations Table
```sql
conversations (
    id UUID PRIMARY KEY,              -- Unique conversation identifier
    title TEXT NOT NULL,              -- Chat title (auto-generated)
    created_at TIMESTAMP,             -- When conversation was created
    updated_at TIMESTAMP,             -- Last message timestamp
    user_id TEXT,                     -- Optional user identification
    metadata JSONB                    -- Additional data storage
)
```

### Messages Table
```sql
messages (
    id UUID PRIMARY KEY,              -- Unique message identifier
    conversation_id UUID,             -- Links to conversations.id
    role TEXT CHECK (role IN ('user', 'assistant')),
    content TEXT NOT NULL,            -- Message content
    created_at TIMESTAMP,             -- When message was sent
    metadata JSONB                    -- Additional message data
)
```

## 🔧 How UUID Organization Works

### Conversation Flow
1. **New Chat Created** → New UUID generated: `550e8400-e29b-41d4-a716-************`
2. **Messages Added** → Each message references conversation UUID
3. **Loading Chat** → Query: `SELECT * FROM messages WHERE conversation_id = 'uuid'`
4. **Switching Chats** → Load different UUID's messages

### Example Data Structure
```javascript
// Conversation in database
{
  id: "550e8400-e29b-41d4-a716-************",
  title: "Quantum Computing Basics",
  created_at: "2024-01-15T10:30:00Z",
  updated_at: "2024-01-15T10:45:00Z"
}

// Messages linked to this conversation
[
  {
    id: "123e4567-e89b-12d3-a456-************",
    conversation_id: "550e8400-e29b-41d4-a716-************",
    role: "user",
    content: "Explain quantum computing",
    created_at: "2024-01-15T10:30:00Z"
  },
  {
    id: "987fcdeb-51a2-43d1-9f12-************",
    conversation_id: "550e8400-e29b-41d4-a716-************", 
    role: "assistant",
    content: "Quantum computing is...",
    created_at: "2024-01-15T10:30:15Z"
  }
]
```

## 🔍 Features Enabled

### ✅ Persistent Chat History
- All conversations saved automatically
- Messages persist across browser sessions
- No data loss when refreshing page

### ✅ UUID-Based Organization
- Each chat has unique identifier
- Fast lookups and switching between chats
- Scalable to millions of conversations

### ✅ Real-time Synchronization
- Messages saved immediately when sent
- Titles updated automatically with `/title` command
- Timestamps tracked for all activities

### ✅ Performance Optimized
- Database indexes for fast queries
- Efficient loading of conversation lists
- Lazy loading of messages when switching chats

## 🛠️ Troubleshooting

### Connection Issues
```javascript
// Check browser console for these messages:
"Supabase client initialized successfully" ✅
"Supabase configuration not found" ❌ - Update supabase-config.js
"Failed to initialize Supabase client" ❌ - Check URL/API key
```

### Database Issues
```sql
-- Test your connection in Supabase SQL Editor:
SELECT COUNT(*) FROM conversations;
SELECT COUNT(*) FROM messages;

-- Should return numbers, not errors
```

### Common Fixes
1. **Wrong URL/API Key**: Double-check credentials in Supabase dashboard
2. **CORS Errors**: Make sure you're using the anon key, not service key
3. **Table Not Found**: Run the setup-database.sql script
4. **Permission Denied**: Check Row Level Security policies

## 📈 Usage Monitoring

### Supabase Dashboard
- **Database** → **Tables** → View your data
- **Authentication** → **Users** → User management (if needed)
- **Settings** → **Usage** → Monitor API calls and storage

### Application Logs
```javascript
// Enable detailed logging in browser console
localStorage.setItem('supabase.debug', 'true');
```

## 🔒 Security Notes

- **API Keys**: Only use anon key in frontend code
- **RLS Policies**: Currently set to allow all operations
- **User Authentication**: Can be added later for multi-user support
- **Data Privacy**: All data stored in your Supabase project

## 🚀 Next Steps

1. **Test the integration** - Create a few conversations
2. **Monitor performance** - Check Supabase dashboard
3. **Add user authentication** - If you want multi-user support
4. **Customize policies** - Restrict access based on your needs

Your chat application now has enterprise-grade database storage with UUID-based organization! 🎉
