# Veritas Chat AI - Powered by Google AI Studio

A modern ChatGPT-style web application built with vanilla HTML, CSS, and JavaScript, powered by Google AI Studio's Gemini 2.5 Flash API.

## 🚀 Features

- **Multi-Agent AI System** - 6 specialized AI agents working collaboratively
- **Real AI Conversations** - Powered by Google's Gemini 2.5 Flash model
- **Intelligent Agent Orchestration** - Automatic workflow coordination for complex requests
- **Modern UI** - Dark theme with smooth animations and progress tracking
- **Multiple Chat Sessions** - Create and manage multiple conversations
- **Responsive Design** - Works on desktop and mobile devices
- **Sidebar Management** - Collapsible sidebar with smooth animations
- **Typing Indicators** - Visual feedback during AI responses with agent progress
- **Markdown Support** - Basic formatting for better readability
- **Quality Assurance** - Built-in validation and quality gates
- **Offline Fallback** - Works with sample responses when API is not configured

## 🔧 Setup Instructions

### 1. Get Your Google AI Studio API Key

1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy your API key

### 2. Configure the Application

1. Open `config.js` in your text editor
2. Find the line: `GEMINI_API_KEY: '',`
3. Paste your API key between the quotes:
   ```javascript
   GEMINI_API_KEY: 'your-api-key-here',
   ```
4. Save the file

### 3. Run the Application

1. Open `index.html` in your web browser
2. Start chatting with the AI!

## 🤖 Multi-Agent System

Veritas features an advanced multi-agent AI system with 10 specialized agents:

### Core Agent Roles
- **Orchestrator** - Coordinates workflows and manages agent collaboration
- **Project Manager** - Handles task breakdown, timelines, and resource planning
- **Planner** - Creates strategic roadmaps and execution plans
- **Sales** - Validates requirements and assesses feasibility
- **Coding** - Provides technical implementation and architecture guidance
- **Validation** - Ensures quality and synthesizes final responses

### Specialized Agent Roles
- **Research** - Information gathering, data analysis, and market research
- **Creative** - Content creation, design, branding, and marketing materials
- **Operations** - Process optimization, automation, and workflow management
- **Analytics** - Data analysis, reporting, metrics, and performance tracking

### How It Works - Sequential Chain-of-Thought
1. **Smart Detection** - System automatically determines when to use multiple agents
2. **Chain Initiation** - Orchestrator analyzes request and routes to ONE starting agent
3. **Sequential Processing** - Each agent processes and routes to the NEXT single agent
4. **Chain Building** - Each agent builds upon previous agents' work in logical sequence
5. **Quality Gates** - Each agent output is validated before proceeding to next agent
6. **Final Synthesis** - Chain continues until completion or routes to Validation agent

### Workflow Examples

**Marketing Strategy Request:**
```
User: "Create a marketing strategy for our new product"
Orchestrator → Research → Creative → Sales → Validation → Final Response
```

**Technical Implementation Request:**
```
User: "Build a user authentication system"
Orchestrator → Planner → Coding → Operations → Validation → Final Response
```

**Business Analysis Request:**
```
User: "Analyze our sales performance and recommend improvements"
Orchestrator → Analytics → Research → Operations → Validation → Final Response
```

Each agent in the chain:
- **Maintains conversation history** from the entire chat session
- **Receives full context** of previous agents' work in the current workflow
- **Adds their specialized contribution** building upon both conversation history and agent outputs
- **Intelligently routes** to the next most appropriate agent
- **Routes to Validation** when the solution is complete

### Context Continuity & Performance
The multi-agent system maintains full conversation context with optimized performance:

**Context Features:**
- Agents remember previous discussions and user preferences
- Follow-up questions build upon earlier conversations
- Complex multi-turn requests are handled seamlessly
- Each agent has access to the complete conversation history

**Performance Optimization:**
- Conversation history is loaded **once** at the start of each workflow
- All agents are initialized with history simultaneously
- No redundant history processing during agent-to-agent handoffs
- Efficient memory usage even with long conversation histories

### Usage
- **Toggle Multi-Agent Mode** - Use the toggle button in the header
- **Automatic Activation** - Complex requests automatically trigger multi-agent processing
- **Progress Tracking** - Watch agents collaborate in real-time

For detailed documentation, see [MULTI-AGENT-SYSTEM.md](MULTI-AGENT-SYSTEM.md)

## 📁 File Structure

```
Veritas Chat AI Online/
├── index.html              # Main HTML file
├── styles.css              # CSS styling
├── script.js               # Main JavaScript functionality
├── config.js               # Configuration file
├── multi-agent-system.js   # Multi-agent AI system
├── multi-agent-tests.js    # Comprehensive test suite
├── test-multi-agent.html   # Multi-agent testing interface
├── supabase-config.js      # Database configuration
├── supabase-client.js      # Database client
├── setup-database.sql     # Database setup script
├── MULTI-AGENT-SYSTEM.md  # Multi-agent documentation
└── README.md               # This file
```

## 🎨 Customization

### Changing the Model

You can modify the AI model in `config.js`:

```javascript
MODEL_NAME: 'gemini-2.5-flash',  // or 'gemini-1.5-pro'
```

### Adjusting AI Behavior

Modify the generation configuration in `config.js`:

```javascript
GENERATION_CONFIG: {
    temperature: 0.7,     // Creativity (0.0 - 1.0)
    topK: 40,            // Token selection diversity
    topP: 0.95,          // Nucleus sampling
    maxOutputTokens: 8192 // Maximum response length
}
```

### Safety Settings

Adjust content filtering in `config.js`:

```javascript
SAFETY_SETTINGS: [
    {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE'  // or 'BLOCK_NONE', 'BLOCK_LOW_AND_ABOVE'
    }
    // ... other categories
]
```

## 🔒 Security Notes

- **Never commit your API key** to version control
- **Keep your API key private** - don't share it publicly
- **Monitor your usage** at [Google AI Studio](https://aistudio.google.com)
- **Set usage limits** if needed to control costs

## 🆘 Troubleshooting

### API Key Issues
- Make sure your API key is correctly pasted in `config.js`
- Check that there are no extra spaces or characters
- Verify your API key is active at Google AI Studio

### CORS Errors
- This app is designed to run locally or on a web server
- If you encounter CORS issues, serve the files through a local web server

### No Responses
- Check the browser console for error messages
- Verify your internet connection
- Ensure your API key has sufficient quota

## 📊 API Usage

- **Free Tier**: Google AI Studio provides generous free usage
- **Rate Limits**: Check current limits at [Google AI Studio](https://aistudio.google.com)
- **Monitoring**: Track your usage in the Google AI Studio dashboard

## 🌟 Features in Detail

### Chat Management
- Create new conversations with the "New chat" button
- Switch between conversations in the sidebar
- Clear current conversation with the trash button

### Sidebar Controls
- Click the hamburger menu (☰) to hide/show the sidebar
- Sidebar slides smoothly with animation
- Icon changes to indicate current state

### Message Formatting
- **Bold text**: `**text**` becomes **text**
- **Italic text**: `*text*` becomes *text*
- **Code**: `` `code` `` becomes `code`
- Line breaks are preserved

## 🔄 Updates

To update the application:
1. Download the latest version
2. Replace all files except `config.js` (to preserve your API key)
3. Check for any new configuration options in the updated `config.js`

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

---

**Powered by Google AI Studio - Gemini 2.5 Flash**
