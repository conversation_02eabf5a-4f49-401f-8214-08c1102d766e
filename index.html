<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
    <title>Veritas Chat AI</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Authentication Screen -->
    <div class="auth-container" id="authContainer">
        <div class="auth-background">
            <div class="auth-card">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-brain"></i>
                        <h1>Veritas Chat AI</h1>
                    </div>
                </div>

                <div class="auth-tabs">
                    <button class="auth-tab active" id="loginTab">Login</button>
                    <button class="auth-tab" id="registerTab">Register</button>
                </div>

                <!-- Login Form -->
                <form class="auth-form" id="loginForm">
                    <div class="form-group">
                        <label for="loginEmail">Email</label>
                        <input type="email" id="loginEmail" required placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" required placeholder="Enter your password">
                    </div>
                    <button type="submit" class="auth-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In
                    </button>
                    <div class="auth-links">
                        <a href="#" class="forgot-password">Forgot password?</a>
                    </div>
                </form>

                <!-- Register Form -->
                <form class="auth-form hidden" id="registerForm">
                    <div class="form-group">
                        <label for="registerEmail">Email</label>
                        <input type="email" id="registerEmail" required placeholder="Enter your email">
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">Password</label>
                        <input type="password" id="registerPassword" required placeholder="Create a password">
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" required placeholder="Confirm your password">
                    </div>
                    <button type="submit" class="auth-btn">
                        <i class="fas fa-user-plus"></i>
                        Create Account
                    </button>
                    <div class="auth-links">
                        <p class="terms-text">By creating an account, you agree to our Terms of Service</p>
                    </div>
                </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Screen -->
    <div class="loading-container" id="loadingContainer" style="display: none;">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-brain"></i>
                <h2>Veritas Chat AI</h2>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <div class="loading-text">
                <p id="loadingText">Initializing your chat experience...</p>
            </div>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <span class="progress-text" id="progressText">0%</span>
            </div>
        </div>
    </div>

    <div class="app-container" id="appContainer" style="display: none;">
        <!-- Sidebar Backdrop for Mobile -->
        <div class="sidebar-backdrop" id="sidebarBackdrop"></div>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="new-chat-btn" id="newChatBtn">
                    <i class="fas fa-plus"></i>
                    New chat
                </button>
            </div>
            
            <div class="chat-history" id="chatHistory">
                <!-- Chat history items will be added here -->
            </div>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <button class="profile-settings-btn" id="profileSettingsBtn" title="Profile Settings">
                        <i class="fas fa-user-cog"></i>
                        <span>Settings</span>
                    </button>
                    <button class="logout-btn" id="logoutBtn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                <div class="powered-by">
                    <div class="ai-branding">
                    </div>
                </div>
            </div>

            <!-- Settings Menu -->
            <div class="settings-menu" id="settingsMenu">
                <div class="settings-header">
                    <h3>Profile Settings</h3>
                    <button class="settings-close-btn" id="settingsCloseBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="settings-content">
                    <div class="settings-section">
                        <div class="settings-item">
                            <label>Email</label>
                            <div class="settings-value" id="settingsUserEmail"><EMAIL></div>
                        </div>
                        <div class="settings-item">
                            <label>Auto-scroll</label>
                            <div class="settings-toggle">
                                <input type="checkbox" id="autoScrollToggle" checked>
                                <span class="toggle-slider"></span>
                            </div>
                        </div>
                    </div>


                    <div class="settings-section danger-section">
                        <div class="settings-item">
                            <div class="danger-setting">
                                <div class="danger-info">
                                    <label>Delete All Chat History</label>
                                    <span class="danger-description">Permanently delete all conversations and messages</span>
                                </div>
                                <button class="settings-btn danger" id="deleteAllChatsBtn">
                                    <i class="fas fa-trash-alt"></i>
                                    Delete All
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="settings-actions">
                        <button class="settings-btn secondary" id="resetSettingsBtn">Reset to Default</button>
                        <button class="settings-btn primary" id="saveSettingsBtn">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="app-title">Veritas Chat AI</h1>
                <div class="header-actions">
                    <button class="multi-agent-toggle" id="multiAgentToggle" title="Toggle Multi-Agent Mode">
                        <i class="fas fa-brain"></i>
                        <span class="toggle-text">AI Team</span>
                    </button>
                    <button class="new-chat-btn-header" id="newChatBtnHeader" title="Start New Chat">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-container" id="chatContainer">
                <!-- Agent Discussion Panel -->
                <div class="agent-discussion-panel" id="agentDiscussionPanel" style="display: none;">
                    <div class="discussion-header">
                        <h3><i class="fas fa-link"></i> Agent Chain of Thought</h3>
                        <button class="discussion-toggle" id="discussionToggle">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="discussion-content" id="discussionContent">
                        <!-- Agent Thinking Status Box -->
                        <div class="agent-thinking-status" id="agentThinkingStatus">
                            <div class="thinking-box">
                                <div class="thinking-agent-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="thinking-content">
                                    <div class="thinking-agent-name" id="thinkingAgentName">ORCHESTRATOR</div>
                                    <div class="thinking-text" id="thinkingText">Analyzing request and coordinating agents</div>
                                    <div class="thinking-progress">
                                        <div class="thinking-progress-bar" id="thinkingProgressBar"></div>
                                    </div>
                                </div>
                                <div class="thinking-dots">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>

                        <!-- Chain visualization -->
                        <div class="chain-visualization" id="chainVisualization">
                            <div class="chain-steps" id="chainSteps">
                                <!-- Chain steps will appear here -->
                            </div>
                        </div>

                        <!-- Traditional discussion messages (fallback) -->
                        <div class="discussion-messages" id="discussionMessages" style="display: none;">
                            <!-- Agent discussion messages will appear here -->
                        </div>


                    </div>
                </div>

                <div class="welcome-message">
                    <div class="welcome-icon">
                        <img src="robot-icon.png" alt="Robot Icon" class="robot-icon-img">
                    </div>
                    <h2>Welcome to Veritas Chat AI</h2>
                    <p>How can I help you today?</p>
                    
                    <div class="example-prompts">
                        <div class="prompt-card" data-prompt="Explain quantum computing in simple terms">
                            <i class="fas fa-atom"></i>
                            <span>Explain quantum computing</span>
                        </div>
                        <div class="prompt-card" data-prompt="Write a creative story about space exploration">
                            <i class="fas fa-rocket"></i>
                            <span>Write a creative story</span>
                        </div>
                        <div class="prompt-card" data-prompt="Help me plan a healthy meal for the week">
                            <i class="fas fa-utensils"></i>
                            <span>Plan a healthy meal</span>
                        </div>
                        <div class="prompt-card" data-prompt="Explain the basics of machine learning">
                            <i class="fas fa-brain"></i>
                            <span>Explain machine learning</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="input-container">
                <div class="input-wrapper">
                    <textarea
                        id="messageInput"
                        rows="1"
                        maxlength="4000"
                        placeholder="Ask me anything... ✨"
                    ></textarea>
                    <div class="input-buttons">
                        <input type="file" id="fileInput" accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.csv,.json" style="display: none;" multiple>
                        <button class="upload-btn" id="uploadBtn" title="Upload documents">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <button class="send-btn" id="sendBtn" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Processing Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Supabase JS Library -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Configuration Files -->
    <script src="config.js"></script>
    <script src="supabase-config.js"></script>
    <script src="supabase-client.js"></script>

    <!-- Multi-Agent System -->
    <script src="multi-agent-system.js"></script>

    <!-- Main Application -->
    <script src="script.js?v=1.1"></script>
</body>
</html>
