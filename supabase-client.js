// Supabase Client and Database Service for Veritas Chat AI
class SupabaseService {
    constructor() {
        // Implement singleton pattern to prevent multiple instances
        if (SupabaseService.instance) {
            return SupabaseService.instance;
        }

        this.supabase = null;
        this.isConnected = false;
        this.initializeClient();

        // Store the instance
        SupabaseService.instance = this;
    }

    initializeClient() {
        // Check if Supabase configuration is available
        if (!SUPABASE_CONFIG.SUPABASE_URL || !SUPABASE_CONFIG.SUPABASE_ANON_KEY) {
            console.warn('Supabase configuration not found. Running in offline mode.');
            return;
        }

        try {
            // Initialize Supabase client (requires Supabase JS library)
            if (typeof supabase !== 'undefined') {
                this.supabase = supabase.createClient(
                    SUPABASE_CONFIG.SUPABASE_URL,
                    SUPABASE_CONFIG.SUPABASE_ANON_KEY
                );
                this.isConnected = true;
                console.log('Supabase client initialized successfully');
            } else {
                console.warn('Supabase JS library not loaded. Please include the Supabase CDN.');
            }
        } catch (error) {
            console.error('Failed to initialize Supabase client:', error);
        }
    }

    // Static method to get the singleton instance
    static getInstance() {
        if (!SupabaseService.instance) {
            SupabaseService.instance = new SupabaseService();
        }
        return SupabaseService.instance;
    }

    // Check if Supabase is available
    isAvailable() {
        return this.isConnected && this.supabase;
    }

    // Generate UUID (fallback if not available from Supabase)
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // CONVERSATION OPERATIONS

    async createConversation(title, userId = null) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const conversationData = {
                title: title,
                user_id: userId,
                metadata: {}
            };

            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .insert([conversationData])
                .select()
                .single();

            if (error) throw error;

            console.log('Conversation created:', data);
            return data;
        } catch (error) {
            console.error('Error creating conversation:', error);
            throw error;
        }
    }

    async getConversation(conversationId) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .select('*')
                .eq('id', conversationId)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error fetching conversation:', error);
            throw error;
        }
    }

    async getAllConversations(userId = null, limit = 50) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            let query = this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .select('*')
                .order('updated_at', { ascending: false })
                .limit(limit);

            if (userId) {
                query = query.eq('user_id', userId);
            }

            const { data, error } = await query;

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching conversations:', error);
            throw error;
        }
    }

    async updateConversationTitle(conversationId, newTitle) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .update({ title: newTitle })
                .eq('id', conversationId)
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error updating conversation title:', error);
            throw error;
        }
    }

    async updateConversation(conversationId, updates) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .update(updates)
                .eq('id', conversationId)
                .select()
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error updating conversation:', error);
            throw error;
        }
    }

    async deleteConversation(conversationId) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .delete()
                .eq('id', conversationId);

            if (error) throw error;
            console.log('Conversation deleted:', conversationId);
            return true;
        } catch (error) {
            console.error('Error deleting conversation:', error);
            throw error;
        }
    }

    // MESSAGE OPERATIONS

    async addMessage(conversationId, role, content, metadata = {}) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const messageData = {
                conversation_id: conversationId,
                role: role,
                content: content,
                metadata: metadata
            };

            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.MESSAGES)
                .insert([messageData])
                .select()
                .single();

            if (error) throw error;

            // Update conversation's updated_at timestamp
            await this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .update({ updated_at: new Date().toISOString() })
                .eq('id', conversationId);

            console.log('Message added:', data);
            return data;
        } catch (error) {
            console.error('Error adding message:', error);
            throw error;
        }
    }

    async updateMessage(messageId, content, metadata = {}) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const updateData = {
                content: content,
                metadata: metadata
                // Note: messages table doesn't have updated_at column
            };

            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.MESSAGES)
                .update(updateData)
                .eq('id', messageId)
                .select()
                .single();

            if (error) throw error;

            console.log('Message updated:', data);
            return data;
        } catch (error) {
            console.error('Error updating message:', error);
            throw error;
        }
    }

    async getMessages(conversationId, limit = 100) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { data, error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.MESSAGES)
                .select('*')
                .eq('conversation_id', conversationId)
                .order('created_at', { ascending: true })
                .limit(limit);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching messages:', error);
            throw error;
        }
    }

    async deleteMessage(messageId) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            const { error } = await this.supabase
                .from(SUPABASE_CONFIG.TABLES.MESSAGES)
                .delete()
                .eq('id', messageId);

            if (error) throw error;
            console.log('Message deleted:', messageId);
            return true;
        } catch (error) {
            console.error('Error deleting message:', error);
            throw error;
        }
    }

    // UTILITY METHODS

    async getConversationWithMessages(conversationId) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            // Get conversation details
            const conversation = await this.getConversation(conversationId);
            
            // Get all messages for this conversation
            const messages = await this.getMessages(conversationId);

            return {
                ...conversation,
                messages: messages
            };
        } catch (error) {
            console.error('Error fetching conversation with messages:', error);
            throw error;
        }
    }

    async searchConversations(searchTerm, userId = null) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            let query = this.supabase
                .from(SUPABASE_CONFIG.TABLES.CONVERSATIONS)
                .select('*')
                .ilike('title', `%${searchTerm}%`)
                .order('updated_at', { ascending: false });

            if (userId) {
                query = query.eq('user_id', userId);
            }

            const { data, error } = await query;

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error searching conversations:', error);
            throw error;
        }
    }



    async searchMemories(searchTerm, userId = null) {
        if (!this.isAvailable()) {
            throw new Error('Supabase not available');
        }

        try {
            let query = this.supabase
                .from(SUPABASE_CONFIG.TABLES.MEMORIES)
                .select('*')
                .ilike('content', `%${searchTerm}%`)
                .order('created_at', { ascending: false });

            if (userId) {
                query = query.eq('user_id', userId);
            }

            const { data, error } = await query;

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error searching memories:', error);
            throw error;
        }
    }


}

// Export the service
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseService;
} else {
    window.SupabaseService = SupabaseService;
}
